package org.iot.pro.device.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.hswebframework.web.api.crud.entity.GenericTreeSortSupportEntity;
import org.hswebframework.web.api.crud.entity.TreeSupportEntity;

import javax.persistence.Column;
import javax.persistence.Table;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025-08-18
 */
@Setter
@Getter
@Table(name="s_ota")
public class OTAEntity {

    @Column
    @Schema(description="ID")
    private Integer id;                  // ID

    @Column
    @Schema(description="升级包名称")
    private String otaName;              // 升级包名称

    @Column
    @Schema(description="升级包类型(1.全量 2.部分)")
    private Integer otaType;             // 升级包类型（1.全量，2.部分）

    @Column
    @Schema(description="固件版本号")
    private String otaVersion;           // 固件版本号

    @Column
    @Schema(description="签名算法(1.MD5)")
    private Integer signType;            // 签名算法(1.MD5)

    @Column
    @Schema(description="升级产品(产品分类)")
    private String equipmentType;        // 升级产品（产品分类）

    @Column
    @Schema(description="协议类型(1.http,2.mqtt,3.bacnet,4.modbus,5.udp)")
    private String agreementType;        // 协议类型（1.http,2.mqtt,3.bacnet,4.modbus,5.udp）

    @Column
    @Schema(description="升级包路径")
    private String otaFilePath;          // 升级包路径

    @Column
    @Schema(description="升级包的md5值")
    private String otaFileMd5;           // 升级包的md5值

    @Column
    @Schema(description="升级说明")
    private String describe;             // 升级说明

    @Column
    @Schema(description="创建者Id")
    private Long createUserId;           // 创建者Id

    @Column
    @Schema(description="创建时间")
    private LocalDateTime createTime;    // 创建时间

    @Column
    @Schema(description="更新者Id")
    private Long updateUserId;           // 更新者Id

    @Column
    @Schema(description="最后更新时间")
    private LocalDateTime updateTime;    // 最后更新时间

    @Column
    @Schema(description="删除标记(1:正常 -1:已删除)")
    private Integer deletedFlag;         // 删除标记(1: 正常 -1: 已删除)

//    @Override
//    public <T extends TreeSupportEntity<Integer>> List<T> getChildren() {
//        return Collections.emptyList();
//    }
}
