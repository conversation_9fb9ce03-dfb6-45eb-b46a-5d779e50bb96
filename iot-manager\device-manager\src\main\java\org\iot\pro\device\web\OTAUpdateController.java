package org.iot.pro.device.web;

import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import org.hswebframework.web.authorization.annotation.Authorize;
import org.hswebframework.web.crud.service.ReactiveCrudService;
import org.hswebframework.web.crud.web.reactive.ReactiveServiceCrudController;
import org.iot.pro.device.entity.OTAEntity;
import org.iot.pro.device.service.OTAService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import reactor.core.publisher.Flux;

/**
 * <AUTHOR>
 * @Date 2025-08-16
 */
@Tag(name = "OTA升级")
@RestController
@RequestMapping("/OTAupdate")
@AllArgsConstructor
public class OTAUpdateController implements ReactiveServiceCrudController<OTAEntity,Integer> {

    private final OTAService otaService;

    @GetMapping("/getAllOTAList")
    @Operation(summary = "获取OTA升级列表")
    @Authorize(merge = false)
    public Flux<OTAEntity> getAllOTAList() {
        return this.otaService
            .createQuery()
            .fetch();
    }





    @Override
    public ReactiveCrudService<OTAEntity, Integer> getService() {
        return otaService;
    }
}
