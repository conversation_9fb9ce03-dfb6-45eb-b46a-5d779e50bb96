package org.iot.pro.device.service;

import org.hswebframework.web.crud.service.GenericReactiveCrudService;
import org.hswebframework.web.crud.service.GenericReactiveTreeSupportCrudService;
import org.hswebframework.web.id.IDGenerator;
import org.iot.pro.device.entity.OTAEntity;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025-08-18
 */
@Service
public class OTAService extends GenericReactiveCrudService<OTAEntity,Integer> implements CommandLineRunner {
    @Override
    public void run(String... args) throws Exception {

    }
}
